extends Node

# Performance monitoring system for the game
var frameTimeHistory = []
var maxHistorySize = 60  # Store 1 second of frame times at 60 FPS

# Performance metrics
var currentFPS = 0.0
var averageFPS = 0.0
var minFPS = 999.0
var maxFPS = 0.0
var frameTimeMs = 0.0

# Object counts
var bulletCount = 0
var enemyCount = 0
var effectCount = 0

# Memory tracking
var memoryUsage = 0
var lastMemoryCheck = 0.0
const MEMORY_CHECK_INTERVAL = 1.0  # Check memory every second

# Performance warnings
var performanceWarnings = []
const MAX_WARNINGS = 10

signal performance_warning(message)

func _ready():
	# Enable performance monitoring
	set_process(true)

func _process(delta):
	updateFrameMetrics(delta)
	updateObjectCounts()
	checkMemoryUsage()
	checkPerformanceThresholds()

func updateFrameMetrics(delta):
	"""Update FPS and frame time metrics"""
	frameTimeMs = delta * 1000.0
	currentFPS = 1.0 / delta if delta > 0 else 0.0
	
	# Add to history
	frameTimeHistory.append(frameTimeMs)
	if frameTimeHistory.size() > maxHistorySize:
		frameTimeHistory.pop_front()
	
	# Calculate average FPS
	if frameTimeHistory.size() > 0:
		var totalFrameTime = 0.0
		for frameTime in frameTimeHistory:
			totalFrameTime += frameTime
		var avgFrameTime = totalFrameTime / frameTimeHistory.size()
		averageFPS = 1000.0 / avgFrameTime if avgFrameTime > 0 else 0.0
	
	# Update min/max FPS
	if currentFPS > maxFPS:
		maxFPS = currentFPS
	if currentFPS < minFPS and currentFPS > 0:
		minFPS = currentFPS

func updateObjectCounts():
	"""Update counts of game objects"""
	if Global.GameScene:
		bulletCount = Global.BulletsOnScreen + Global.EnemyBulletsOnScreen
		enemyCount = Global.EnemiesAttacking
		
		# Count effects (sparkles, explosions, etc.)
		effectCount = 0
		if Global.GameScene.bulletManager:
			effectCount = Global.GameScene.bulletManager.getActiveBulletCount()

func checkMemoryUsage():
	"""Monitor memory usage"""
	var currentTime = Time.get_ticks_msec() / 1000.0
	if currentTime - lastMemoryCheck >= MEMORY_CHECK_INTERVAL:
		memoryUsage = OS.get_static_memory_usage_by_type()
		lastMemoryCheck = currentTime

func checkPerformanceThresholds():
	"""Check for performance issues and emit warnings"""
	# Low FPS warning
	if currentFPS < 30.0 and currentFPS > 0:
		addPerformanceWarning("Low FPS detected: " + str(int(currentFPS)) + " FPS")
	
	# High bullet count warning
	if bulletCount > Config.MaxPlayerBulletsOnScreen + Config.MaxEnemyBulletsOnScreen:
		addPerformanceWarning("High bullet count: " + str(bulletCount))
	
	# High frame time warning
	if frameTimeMs > 33.33:  # More than 33ms = less than 30 FPS
		addPerformanceWarning("High frame time: " + str(frameTimeMs) + "ms")

func addPerformanceWarning(message: String):
	"""Add a performance warning"""
	var currentTime = Time.get_ticks_msec()
	var warning = {
		"message": message,
		"timestamp": currentTime
	}
	
	performanceWarnings.append(warning)
	if performanceWarnings.size() > MAX_WARNINGS:
		performanceWarnings.pop_front()
	
	emit_signal("performance_warning", message)
	print("Performance Warning: " + message)

func getPerformanceReport() -> Dictionary:
	"""Get a comprehensive performance report"""
	return {
		"fps": {
			"current": currentFPS,
			"average": averageFPS,
			"min": minFPS,
			"max": maxFPS
		},
		"frameTime": {
			"current": frameTimeMs,
			"history": frameTimeHistory.duplicate()
		},
		"objects": {
			"bullets": bulletCount,
			"enemies": enemyCount,
			"effects": effectCount
		},
		"memory": {
			"usage": memoryUsage
		},
		"warnings": performanceWarnings.duplicate()
	}

func resetMetrics():
	"""Reset all performance metrics"""
	frameTimeHistory.clear()
	minFPS = 999.0
	maxFPS = 0.0
	performanceWarnings.clear()

func getOptimizationSuggestions() -> Array:
	"""Get suggestions for performance optimization"""
	var suggestions = []
	
	if averageFPS < 45:
		suggestions.append("Consider reducing bullet limits or effect quality")
	
	if bulletCount > 80:
		suggestions.append("Too many bullets on screen - implement better culling")
	
	if frameTimeMs > 25:
		suggestions.append("Frame time too high - optimize game loop")
	
	if memoryUsage > 100 * 1024 * 1024:  # 100MB
		suggestions.append("High memory usage - check for memory leaks")
	
	return suggestions

func enableDebugDisplay():
	"""Enable on-screen performance display"""
	var label = Label.new()
	label.name = "PerformanceDisplay"
	label.anchor_left = 0.0
	label.anchor_top = 0.0
	label.anchor_right = 0.0
	label.anchor_bottom = 0.0
	label.margin_left = 10
	label.margin_top = 10
	label.add_color_override("font_color", Color.YELLOW)
	
	get_tree().current_scene.add_child(label)
	
	# Update display every frame
	var timer = Timer.new()
	timer.wait_time = 0.1
	timer.timeout.connect(self, "_updateDebugDisplay")
	timer.autostart = true
	add_child(timer)

func _updateDebugDisplay():
	"""Update the debug performance display"""
	var display = get_tree().current_scene.get_node_or_null("PerformanceDisplay")
	if display:
		var text = "FPS: %d (Avg: %d)\n" % [int(currentFPS), int(averageFPS)]
		text += "Frame Time: %.1fms\n" % frameTimeMs
		text += "Bullets: %d\n" % bulletCount
		text += "Enemies: %d\n" % enemyCount
		text += "Effects: %d" % effectCount
		display.text = text
