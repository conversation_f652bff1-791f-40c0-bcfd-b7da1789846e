extends Area2D

# Optimized enemy bullet class using object pooling
var randomJiggle = true
var doJiggle = false
export var canKillPlayer = true
var velocity = Vector2(0, 1)

# Pool management
var poolName = ""
var isFromPool = false
var isDestroyed = false

# Performance optimization
var lastUpdateTime = 0.0
const UPDATE_INTERVAL = 0.016  # ~60 FPS

func isDeadly():
	return canKillPlayer

func canClash():
	return false

func initEnemyBullet(startPos: Vector2, bulletVelocity: Vector2 = Vector2(0, 1)):
	"""Initialize enemy bullet for reuse from pool"""
	position = startPos
	velocity = bulletVelocity
	isDestroyed = false
	randomJiggle = true
	doJiggle = false
	canKillPlayer = true
	lastUpdateTime = 0.0
	
	# Reset visual state
	modulate = Color.WHITE
	scale = Vector2.ONE
	rotation = 0
	
	# Enable processing
	set_process(true)
	set_physics_process(true)
	visible = true
	
	# Determine jiggle behavior
	if randomJiggle and (randf() < Config.EnemyBulletJiggleChance):
		doJiggle = true

func destroy():
	if isDestroyed:
		return
	
	isDestroyed = true
	Global.EnemyBulletsOnScreen -= 1
	Global.EnemyBulletsOnScreen = max(0, Global.EnemyBulletsOnScreen)
	
	# Return to pool instead of queue_free
	if isFromPool and poolName != "":
		Global.returnToPool(poolName, self)
	else:
		queue_free()

func isEnemyBullet():
	return true

func getBulletSpeed():
	var speed = Config.EnemyBulletSpeed
	
	# slightly higher on normal
	if Global.GameScene.getProperDifficulty() == Global.GameDifficulty.NORMAL:
		speed = int(float(speed) * 0.8)
	
	# if mouse control, speed up bullets to equalize challenge
	if Global.OptionsData.controlType == Global.GameControlMode.MOUSE:
		speed = int(float(speed) * 1.2)
	
	# lower bullet speed on easy
	if Global.GameScene.getProperDifficulty() == Global.GameDifficulty.EASY:
		speed = int(float(speed) * 0.5)
	
	return speed

func moveBullet(delta):
	# move bullet
	var _velocity = velocity
	
	if doJiggle:
		_velocity.x += (randf() - 0.5)
	
	_velocity = _velocity * getBulletSpeed()
	position += _velocity * delta
	
	# destroy bullet if it goes off screen
	if position.y > Global.getWindowSize().y + 100:
		destroy()

func _ready():
	# Reduced audio processing for performance
	if $AudioStreamPlayer2D != null:
		$AudioStreamPlayer2D.autoplay = false
		if Global.OptionsData.isSoundOn and randf() < 0.3:  # Only play sound 30% of the time
			$AudioStreamPlayer2D.play()

func _process(delta):
	# Throttle updates for better performance
	lastUpdateTime += delta
	if lastUpdateTime < UPDATE_INTERVAL:
		return
	
	var actualDelta = lastUpdateTime
	lastUpdateTime = 0.0
	
	moveBullet(actualDelta)
