extends Node

# Spatial partitioning system for efficient collision detection
# Divides the game world into a grid to reduce collision checks from O(n²) to O(n)

var gridSize = Vector2(64, 64)  # Size of each grid cell
var worldSize = Vector2(1024, 576)  # Game world size
var gridWidth = 0
var gridHeight = 0
var grid = []

# Object tracking
var trackedObjects = {}

func _ready():
	initializeGrid()

func initializeGrid():
	"""Initialize the spatial grid"""
	gridWidth = int(ceil(worldSize.x / gridSize.x))
	gridHeight = int(ceil(worldSize.y / gridSize.y))
	
	grid.clear()
	for y in range(gridHeight):
		var row = []
		for x in range(gridWidth):
			row.append([])
		grid.append(row)

func getGridPosition(worldPos: Vector2) -> Vector2:
	"""Convert world position to grid coordinates"""
	var gridX = int(clamp(worldPos.x / gridSize.x, 0, gridWidth - 1))
	var gridY = int(clamp(worldPos.y / gridSize.y, 0, gridHeight - 1))
	return Vector2(gridX, gridY)

func addObject(obj: Node, objType: String):
	"""Add object to spatial grid"""
	if not obj or not is_instance_valid(obj):
		return
	
	var gridPos = getGridPosition(obj.global_position)
	var gridX = int(gridPos.x)
	var gridY = int(gridPos.y)
	
	if gridX >= 0 and gridX < gridWidth and gridY >= 0 and gridY < gridHeight:
		grid[gridY][gridX].append({
			"object": obj,
			"type": objType,
			"lastPos": obj.global_position
		})
		
		trackedObjects[obj] = {
			"gridPos": gridPos,
			"type": objType
		}

func removeObject(obj: Node):
	"""Remove object from spatial grid"""
	if not trackedObjects.has(obj):
		return
	
	var objData = trackedObjects[obj]
	var gridPos = objData.gridPos
	var gridX = int(gridPos.x)
	var gridY = int(gridPos.y)
	
	if gridX >= 0 and gridX < gridWidth and gridY >= 0 and gridY < gridHeight:
		var cell = grid[gridY][gridX]
		for i in range(cell.size() - 1, -1, -1):
			if cell[i].object == obj:
				cell.remove(i)
				break
	
	trackedObjects.erase(obj)

func updateObject(obj: Node):
	"""Update object position in spatial grid"""
	if not trackedObjects.has(obj) or not is_instance_valid(obj):
		return
	
	var objData = trackedObjects[obj]
	var oldGridPos = objData.gridPos
	var newGridPos = getGridPosition(obj.global_position)
	
	# Only update if object moved to a different grid cell
	if oldGridPos != newGridPos:
		removeObject(obj)
		addObject(obj, objData.type)

func getNearbyObjects(obj: Node, searchRadius: int = 1) -> Array:
	"""Get objects near the given object within search radius"""
	if not trackedObjects.has(obj):
		return []
	
	var objData = trackedObjects[obj]
	var centerGridPos = objData.gridPos
	var nearbyObjects = []
	
	# Search in a radius around the object's grid position
	for y in range(max(0, centerGridPos.y - searchRadius), min(gridHeight, centerGridPos.y + searchRadius + 1)):
		for x in range(max(0, centerGridPos.x - searchRadius), min(gridWidth, centerGridPos.x + searchRadius + 1)):
			var cell = grid[y][x]
			for item in cell:
				if item.object != obj and is_instance_valid(item.object):
					nearbyObjects.append(item)
	
	return nearbyObjects

func getObjectsInRegion(topLeft: Vector2, bottomRight: Vector2) -> Array:
	"""Get all objects in a rectangular region"""
	var startGrid = getGridPosition(topLeft)
	var endGrid = getGridPosition(bottomRight)
	var objects = []
	
	for y in range(int(startGrid.y), int(endGrid.y) + 1):
		for x in range(int(startGrid.x), int(endGrid.x) + 1):
			if y >= 0 and y < gridHeight and x >= 0 and x < gridWidth:
				var cell = grid[y][x]
				for item in cell:
					if is_instance_valid(item.object):
						objects.append(item)
	
	return objects

func clearGrid():
	"""Clear all objects from the grid"""
	for y in range(gridHeight):
		for x in range(gridWidth):
			grid[y][x].clear()
	trackedObjects.clear()

func getGridStats() -> Dictionary:
	"""Get statistics about the spatial grid for debugging"""
	var totalObjects = 0
	var maxObjectsInCell = 0
	var occupiedCells = 0
	
	for y in range(gridHeight):
		for x in range(gridWidth):
			var cellSize = grid[y][x].size()
			totalObjects += cellSize
			if cellSize > 0:
				occupiedCells += 1
			if cellSize > maxObjectsInCell:
				maxObjectsInCell = cellSize
	
	return {
		"totalObjects": totalObjects,
		"maxObjectsInCell": maxObjectsInCell,
		"occupiedCells": occupiedCells,
		"totalCells": gridWidth * gridHeight,
		"gridUtilization": float(occupiedCells) / float(gridWidth * gridHeight)
	}

# Optimized collision detection using spatial partitioning
func checkCollisions(objType1: String, objType2: String) -> Array:
	"""Check collisions between two object types efficiently"""
	var collisions = []
	
	# Only check occupied cells
	for y in range(gridHeight):
		for x in range(gridWidth):
			var cell = grid[y][x]
			if cell.size() < 2:  # Need at least 2 objects for collision
				continue
			
			var type1Objects = []
			var type2Objects = []
			
			# Separate objects by type
			for item in cell:
				if not is_instance_valid(item.object):
					continue
				if item.type == objType1:
					type1Objects.append(item.object)
				elif item.type == objType2:
					type2Objects.append(item.object)
			
			# Check collisions between the two types
			for obj1 in type1Objects:
				for obj2 in type2Objects:
					if obj1.global_position.distance_to(obj2.global_position) < 32:  # Collision threshold
						collisions.append([obj1, obj2])
	
	return collisions
