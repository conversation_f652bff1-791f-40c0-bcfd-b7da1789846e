extends Area2D

# Optimized bullet class using object pooling
var Sparkle = preload("res://scenes/Sparkle.tscn")
var Explosion = preload("res://scenes/Explosion_2.tscn")

# Performance optimizations
var isDestroyed = false
var isPassThrough = false
var canClash = false
var doCountTowardBulletsOnScreen = true
var Speed = Config.BulletSpeed
var velocity = Vector2(0, -1)
var wasLogged = false

# Pool management
var poolName = ""
var isFromPool = false

func initBullet(startPos: Vector2, bulletSpeed: float = Config.BulletSpeed, bulletVelocity: Vector2 = Vector2(0, -1)):
	"""Initialize bullet for reuse from pool"""
	position = startPos
	Speed = bulletSpeed
	velocity = bulletVelocity
	isDestroyed = false
	wasLogged = false
	isPassThrough = false
	canClash = false
	doCountTowardBulletsOnScreen = true
	
	# Reset visual state
	modulate = Color.WHITE
	scale = Vector2.ONE
	rotation = 0
	
	# Enable processing
	set_process(true)
	set_physics_process(true)
	visible = true

func destroy(force = false):
	if isDestroyed:
		return false
	
	isDestroyed = true
	
	# if we don't force the bullet to be destroyed, we only destroy it if it's not a passthrough bullet
	if not force and isPassThrough:
		return false
	
	if doCountTowardBulletsOnScreen:
		Global.GameScene.BulletsOnScreen -= 1
		Global.GameScene.BulletsOnScreen = max(0, Global.GameScene.BulletsOnScreen)
	
	# Create sparkle effect less frequently for performance
	if randf() < 0.7:  # Only 70% of bullets create sparkles
		createSparkleEffect()
	
	# Return to pool instead of queue_free
	if isFromPool and poolName != "":
		Global.returnToPool(poolName, self)
	else:
		queue_free()

func createSparkleEffect():
	"""Optimized sparkle creation"""
	var sparkle = Global.getPooledObject("sparkle", Sparkle)
	sparkle.position = position
	Global.GameScene.add_child(sparkle)

func getDamagePoint():
	if Global.GameScene.isBulletOP:
		return 1000
	
	var result = 10 * ShipSpecs.getSpecs().weapon_strength_multiplier
	
	# if easy mode, make bullets twice as powerful
	if Global.GameScene.difficulty == Global.GameDifficulty.EASY:
		result = result * 2
	
	return result

func getSpeedMod():
	return 1.0

func setSpeed(speed):
	Speed = speed

func setXVelocity(xVelocity):
	velocity.x = xVelocity

func _ready():
	if Global.GameScene and Global.GameScene.levelConductor:
		Global.GameScene.levelConductor.logBullet()
	velocity = Vector2(0, -1)
	var _c = connect("area_entered", self, "_on_hit")

func _on_hit(target):
	# Optimized hit detection
	if not target or isDestroyed:
		return
	
	if target.has_method("isEnemyBullet") and target.isEnemyBullet():
		if canClash:
			Global.callIfExists(target, "destroy")
			destroy(true)
	elif target.has_method("causeDamage"):
		destroy()

# Optimized movement with reduced calculations
var lastUpdateTime = 0.0
const UPDATE_INTERVAL = 0.016  # ~60 FPS

func _process(delta):
	# Throttle updates for better performance
	lastUpdateTime += delta
	if lastUpdateTime < UPDATE_INTERVAL:
		return
	
	var actualDelta = lastUpdateTime
	lastUpdateTime = 0.0
	
	# Move bullet
	var _velocity = velocity.normalized() * Speed
	_velocity.x = velocity.x * Speed
	position += _velocity * actualDelta * Vector2(1, getSpeedMod())
	
	# destroy bullet if it goes off screen
	if position.y < -100:
		if not wasLogged and Global.GameScene and Global.GameScene.levelConductor:
			Global.GameScene.levelConductor.logBullet(true)
			wasLogged = true
		destroy(true)
