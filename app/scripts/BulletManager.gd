extends Node

# High-performance bullet management system
var BulletScene = preload("res://scenes/Bullet.tscn")
var BulletSuperScene = preload("res://scenes/BulletSuper.tscn")
var BulletHomingScene = preload("res://scenes/BulletHoming.tscn")
var BulletLaserScene = preload("res://scenes/BulletLaser.tscn")
var EnemyBulletScene = preload("res://scenes/EnemyBullet_1.tscn")
var SparkleScene = preload("res://scenes/Sparkle.tscn")

# Bullet pools
const POOL_SIZES = {
	"player_bullet": 100,
	"player_bullet_super": 50,
	"player_bullet_homing": 50,
	"player_bullet_laser": 30,
	"enemy_bullet": 200,
	"sparkle": 150
}

# Performance tracking
var activeBullets = []
var bulletUpdateIndex = 0
const BULLETS_PER_FRAME = 10  # Process only N bullets per frame

func _ready():
	# Pre-populate pools for better performance
	initializePools()

func initializePools():
	"""Pre-create objects for pools to avoid runtime allocation"""
	for poolName in POOL_SIZES.keys():
		var poolSize = POOL_SIZES[poolName]
		var scene = getSceneForPool(poolName)
		
		if scene:
			for i in range(poolSize):
				var obj = scene.instance()
				if obj.has_method("initBullet") or obj.has_method("initEnemyBullet"):
					obj.isFromPool = true
					obj.poolName = poolName
				Global.returnToPool(poolName, obj)

func getSceneForPool(poolName: String) -> PackedScene:
	match poolName:
		"player_bullet":
			return BulletScene
		"player_bullet_super":
			return BulletSuperScene
		"player_bullet_homing":
			return BulletHomingScene
		"player_bullet_laser":
			return BulletLaserScene
		"enemy_bullet":
			return EnemyBulletScene
		"sparkle":
			return SparkleScene
		_:
			return null

func createPlayerBullet(bulletType: int, startPosition: Vector2) -> Node:
	"""Create optimized player bullet"""
	var poolName = getPoolNameForBulletType(bulletType)
	var scene = getSceneForPool(poolName)
	
	if not scene:
		return null
	
	var bullet = Global.getPooledObject(poolName, scene)
	
	# Initialize bullet based on type
	if bullet.has_method("initBullet"):
		bullet.initBullet(startPosition)
		bullet.isFromPool = true
		bullet.poolName = poolName
	
	# Set bullet-specific properties
	configureBulletByType(bullet, bulletType)
	
	return bullet

func createEnemyBullet(startPosition: Vector2, bulletVelocity: Vector2 = Vector2(0, 1)) -> Node:
	"""Create optimized enemy bullet"""
	var bullet = Global.getPooledObject("enemy_bullet", EnemyBulletScene)
	
	if bullet.has_method("initEnemyBullet"):
		bullet.initEnemyBullet(startPosition, bulletVelocity)
		bullet.isFromPool = true
		bullet.poolName = "enemy_bullet"
	
	return bullet

func getPoolNameForBulletType(bulletType: int) -> String:
	match bulletType:
		Global.PlayerBulletTypes.STRONG_SINGLE, Global.PlayerBulletTypes.STRONG_TRIPPLE:
			return "player_bullet_super"
		Global.PlayerBulletTypes.HOMING_SINGLE, Global.PlayerBulletTypes.HOMING_TRIPPLE, Global.PlayerBulletTypes.HOMING_TRIPPLE_SUPER:
			return "player_bullet_homing"
		Global.PlayerBulletTypes.LASER_SINGLE:
			return "player_bullet_laser"
		_:
			return "player_bullet"

func configureBulletByType(bullet: Node, bulletType: int):
	"""Configure bullet properties based on type"""
	match bulletType:
		Global.PlayerBulletTypes.SINGLE:
			bullet.canClash = true
		Global.PlayerBulletTypes.DOUBLE, Global.PlayerBulletTypes.TRIPPLE:
			bullet.canClash = true
		Global.PlayerBulletTypes.STRONG_SINGLE, Global.PlayerBulletTypes.STRONG_TRIPPLE:
			bullet.canClash = true
		Global.PlayerBulletTypes.HOMING_SINGLE, Global.PlayerBulletTypes.HOMING_TRIPPLE, Global.PlayerBulletTypes.HOMING_TRIPPLE_SUPER:
			if bullet.has_method("start"):
				bullet.start()
		Global.PlayerBulletTypes.LASER_SINGLE:
			bullet.canClash = true

# Optimized bullet processing - spread across multiple frames
func _process(delta):
	if activeBullets.size() == 0:
		return
	
	var bulletsToProcess = min(BULLETS_PER_FRAME, activeBullets.size())
	var endIndex = min(bulletUpdateIndex + bulletsToProcess, activeBullets.size())
	
	for i in range(bulletUpdateIndex, endIndex):
		if i < activeBullets.size() and is_instance_valid(activeBullets[i]):
			var bullet = activeBullets[i]
			if bullet.has_method("updateBullet"):
				bullet.updateBullet(delta)
	
	bulletUpdateIndex = endIndex
	if bulletUpdateIndex >= activeBullets.size():
		bulletUpdateIndex = 0
		# Clean up invalid bullets
		cleanupInvalidBullets()

func cleanupInvalidBullets():
	"""Remove invalid bullets from active list"""
	var validBullets = []
	for bullet in activeBullets:
		if is_instance_valid(bullet) and not bullet.isDestroyed:
			validBullets.append(bullet)
	activeBullets = validBullets

func addActiveBullet(bullet: Node):
	"""Add bullet to active tracking list"""
	activeBullets.append(bullet)

func removeActiveBullet(bullet: Node):
	"""Remove bullet from active tracking list"""
	var index = activeBullets.find(bullet)
	if index >= 0:
		activeBullets.remove(index)

func clearAllBullets():
	"""Clear all active bullets and return them to pools"""
	for bullet in activeBullets:
		if is_instance_valid(bullet):
			if bullet.has_method("destroy"):
				bullet.destroy(true)
	activeBullets.clear()
	bulletUpdateIndex = 0

func getActiveBulletCount() -> int:
	return activeBullets.size()
