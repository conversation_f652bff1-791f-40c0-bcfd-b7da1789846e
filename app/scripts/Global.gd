extends Node

enum EnemyMode {
	INIT,
	ENTRY,
	<PERSON><PERSON><PERSON><PERSON>,
	ID<PERSON>,
	ATTAC<PERSON>,
	DEAD
}

enum GameControlMode {
	KEYS=0,
	MOUSE=1,
	MOUSE_FOLLOW=2
}

enum GameMode {
	CAMPAIGN=0,
	FLOW=1
}

enum PlayerWingType {
	LEFT,
	RIGHT
}

enum PowerupType {
	EXTRA_SPEED,
	EXTRA_BULLET,
	AUTOFIRE,
	WEAPON_1,
	WEAPON_2,
	WEAPON_3,
	WEAPON_4,
	WEAPON_5,
	WEAPON_6,
	WEAPON_7,
	WEAPON_8,
	WEAPON_9,
	TIMED_SUPER_RAPIDFIRE,
	TIMED_INVINCIBILITY,
	TIMED_EXPLODE_TO_CRYSTAL,
	<PERSON>UC<PERSON>,
	CRISTAL_DOUBLER,
	MINE_FIELD,
	A,
	B,
	C,
	CURSE,
	WING1,
	WING2,
	WING3,
	BULLET_HELL,
	CRYSTAL_MAGNET,
	SHIELD
}

# the weapon order counts!!!!
enum PlayerBulletTypes {
	<PERSON>ING<PERSON>,
	<PERSON>O<PERSON><PERSON><PERSON>,
	TRI<PERSON><PERSON>,
	STRONG_SINGLE,
	STRONG_TRIPPLE,
	HOMING_SINGLE,
	HOMING_TRIPPLE,
	HOMING_TRIPPLE_SUPER,
	LASER_SINGLE,
}

const StatsKeys = {
	LEVEL = 100,
	DURATION = 101,
	GEMS = 102,
	ACCURACY = 103,
	ENTRY_KILLS = 104,
	ROWS_CLEARED = 105,
	CLOSE_KILLS = 106,
	DIFFICULTY = 107,
	REAL_DIFFICULTY = 108, 
	SCORE = 109,
	IS_HIGH_SCORE = 110
}


var bulletAutofireConf = {
	str(PlayerBulletTypes.SINGLE): 1500,
	str(PlayerBulletTypes.DOUBLE): 1500,
	str(PlayerBulletTypes.TRIPPLE): 1500,
	str(PlayerBulletTypes.STRONG_SINGLE): 800,
	str(PlayerBulletTypes.STRONG_TRIPPLE): 1500,
	str(PlayerBulletTypes.HOMING_SINGLE): 1200,
	str(PlayerBulletTypes.HOMING_TRIPPLE): 1500,
	str(PlayerBulletTypes.HOMING_TRIPPLE_SUPER): 1200,
	str(PlayerBulletTypes.LASER_SINGLE): 1200,
}

func getBulletAutofireConf(bulletType):
	return bulletAutofireConf[str(bulletType)]


enum CrystalType {
	c5,
	c10,
	c20,
	c50,
	c100,
	c200
}

enum DiscoBallState {
	NIL,
	MERCY,
	DESTROYED
}

func getSpeedIndex(realSpeed):
	return int((realSpeed-Config.ShipSpeedBase)/Config.ShipSpeedIncrement)

func getBulletIndex(realBullets):
	return int(realBullets-Config.MaxBulletsBase)

const KEY_OPTIONS_STORAGE = "options"

func getMaxWingCount():
	return ShipSpecs.getSpecs().max_wing_count

func _ready():
	# load save default files
	self.OptionsData = Storage.LoadData(KEY_OPTIONS_STORAGE, self.OptionsData, true)
	OS.window_fullscreen = OptionsData.isFullscreenOn
	pass

func saveOptions(chMusic = false):

	Storage.SaveData(KEY_OPTIONS_STORAGE, self.OptionsData)

	# play stop music
	if(self.OptionsData.isMusicOn):
		if(chMusic):
			self.playMusicInstantly(self.lastPlayerRef)
	else:
		if(chMusic):
			self.stopMusicInstantly(self.lastPlayerRef)

	# apply full screen stuff
	OS.window_fullscreen = OptionsData.isFullscreenOn

var OptionsData = {
	"isMusicOn": true,
	"isSoundOn": true,
	"isFullscreenOn": true,
	"controlType": GameControlMode.KEYS,
	"gameMode": GameMode.CAMPAIGN
}

var lastMusic = "menu"
var musicStream = SoundManager.music_menu
var lastPlayerRef = null

var _musicTween = null

func _fadeMusicOut(playerRef, duration = Config.MusicFadeSeconds):
	_musicTween = self.createTween(playerRef.get_parent())
	_musicTween.interpolate_property(playerRef,"volume_db",playerRef.volume_db,-80,duration,Tween.TRANS_LINEAR, Tween.EASE_IN)
	_musicTween.connect("tween_all_completed",self,"stopMusicInstantly",[playerRef])
	_musicTween.start();

func _fadeMusicIn(playerRef, streamKey = null, duration = Config.MusicFadeSeconds):

	if(is_instance_valid(playerRef)):

		playerRef.volume_db = -80
		playMusicInstantly(playerRef, streamKey)

		_musicTween = self.createTween(playerRef.get_parent())
		_musicTween.interpolate_property(playerRef,"volume_db",-80,Config.MusicMaxVolume,duration,Tween.TRANS_LINEAR, Tween.EASE_IN)
		_musicTween.start();

func playMusic(playerRef, streamKey = null):

	if(lastMusic==streamKey && playerRef.is_playing()):
		return
	
	_fadeMusicOut(playerRef)
	setTimeout(self, Config.MusicFadeSeconds*1.1, self, "_fadeMusicIn",[playerRef, streamKey])


func playMusicInstantly(playerRef, streamKey = null):

	if(!OptionsData.isMusicOn):
		return

	self.lastPlayerRef = playerRef

	if(lastMusic==streamKey && playerRef.is_playing()):
		return
	
	if not streamKey:
		streamKey=lastMusic

	lastMusic = streamKey
	self.musicStream = SoundManager["music_"+streamKey]

	playerRef.stream = self.musicStream
	playerRef.volume_db = Config.MusicMaxVolume
	playerRef.play()

func stopMusic(playerRef):
	_fadeMusicOut(playerRef)

func stopMusicInstantly(playerRef):
	self.lastPlayerRef = playerRef
	playerRef.stop();
	
var colorToFrame = {
	"gray": 0,
	"green": 1,
	"blue": 2,
	"purple": 3,
	"red": 4,
	"yellow": 5
}

enum PlayerEffect {
	SUPER_RAPIDFIRE,
	INVINCIBILITY,
	CRYSTAL_EXPLOSION,
	WEAPON_DISABLED,
	LUCK,
	SLOTH_MODE,
	CRYSTAL_MAGNET
}

func isMouseControl():
	return Global.OptionsData.controlType == Global.GameControlMode.MOUSE || Global.OptionsData.controlType == Global.GameControlMode.MOUSE_FOLLOW

func hideMouse(doHide, _forceState = false):
	Input.mouse_mode = ifelse(doHide, Input.MOUSE_MODE_CONFINED , Input.MOUSE_MODE_VISIBLE)
	Input.set_default_cursor_shape(ifelse(doHide, Input.CURSOR_CROSS, Input.CURSOR_ARROW))

var maxPowerupChance = 1000

var powerups = {
	PowerupType.EXTRA_BULLET: {
		"type": "stat",
		"color": "green",
		"chance": 50,
		"char": "B",
		"extra_data": null,
		"text": "Bullet +",
		"freq_mod": 1.0,
		"tts": SoundManager.tts_extra_bullet
	},
	PowerupType.EXTRA_SPEED: {
		"type": "stat",
		"color": "blue",
		"chance": 40,
		"char": "S",
		"extra_data": null,
		"text": "Speed +",
		"freq_mod": 1.0,
		"tts": SoundManager.tts_extra_speed
	},
	PowerupType.SHIELD: {
		"type": "stat",
		"color": "green",
		"chance": 600,
		"char": "SH",
		"extra_data": null,
		"text": "Shield",
		"freq_mod": 1.0,
		"tts": SoundManager.tts_shield
	},
	PowerupType.WING1: {
		"type": "stat",
		"color": "blue",
		"chance": 500,
		"char": "W1",
		"extra_data": null,
		"text": "Simple Wing",
		"freq_mod": 1.0,
		"tts": SoundManager.tts_simple_wing
	},
	PowerupType.WING2: {
		"type": "stat",
		"color": "green",
		"chance": 600,
		"char": "W2",
		"extra_data": null,
		"freq_mod": 1.0,
		"text": "Super Wing",
		"tts": SoundManager.tts_super_wing
	},
	PowerupType.WING3: {
		"type": "stat",
		"color": "red",
		"chance": 800,
		"char": "W3",
		"freq_mod": 1.0,
		"extra_data": null,
		"text": "Homing Wing",
		"tts": SoundManager.tts_homing_wing
	},
	PowerupType.BULLET_HELL: {
		"type": "instant",
		"color": "red",
		"chance": 400,
		"freq_mod": 1.0,
		"char": "BH",
		"extra_data": null,
		"text": "Bullet Hell",
		"tts": SoundManager.tts_bullet_hell
	},
	PowerupType.AUTOFIRE: {
		"type": "instant",
		"color": "green",
		"chance": 280,
		"freq_mod": 1.0,
		"char": "A",
		"extra_data": null,
		"text": "Auto Fire",
		"tts": SoundManager.tts_auto_fire
	},
	PowerupType.WEAPON_1: {
		"type": "weapon",
		"color": "green",
		"chance": 100,
		"freq_mod": 1.0,
		"char": "1",
		"extra_data": PlayerBulletTypes.SINGLE,
		"text": "Single",
		"tts": SoundManager.tts_single
	},
	PowerupType.WEAPON_2: {
		"type": "weapon",
		"color": "green",
		"chance": 180,
		"freq_mod": 0.9,
		"char": "2",
		"extra_data": PlayerBulletTypes.DOUBLE,
		"text": "Double",
		"tts": SoundManager.tts_double
	},
	PowerupType.WEAPON_3: {
		"type": "weapon",
		"color": "green",
		"chance": 200,
		"freq_mod": 0.8,
		"char": "3",
		"extra_data": PlayerBulletTypes.TRIPPLE,
		"text": "Tripple",
		"tts": SoundManager.tts_tripple
	},
	PowerupType.WEAPON_4: {
		"type": "weapon",
		"color": "blue",
		"chance": 400,
		"freq_mod": 1.0,
		"char": "S1",
		"extra_data": PlayerBulletTypes.STRONG_SINGLE,
		"text": "Super Single",
		"tts": SoundManager.tts_super_bullet
	},
	PowerupType.WEAPON_5: {
		"type": "weapon",
		"color": "blue",
		"chance": 410,
		"freq_mod": 0.7,
		"char": "S3",
		"extra_data": PlayerBulletTypes.STRONG_TRIPPLE,
		"text": "Super Tripple",
		"tts": SoundManager.tts_super_tripple
	},
	PowerupType.WEAPON_6: {
		"type": "weapon",
		"color": "purple",
		"chance": 611,
		"freq_mod": 1.0,
		"char": "H",
		"extra_data": PlayerBulletTypes.HOMING_SINGLE,
		"text": "Homing Single",
		"tts": SoundManager.tts_homing_single
	},
	PowerupType.WEAPON_7: {
		"type": "weapon",
		"color": "purple",
		"chance": 633,
		"freq_mod": 0.6,
		"char": "H3",
		"extra_data": PlayerBulletTypes.HOMING_TRIPPLE,
		"text": "Homing Tripple",
		"tts": SoundManager.tts_homing_tripple
	},
	PowerupType.WEAPON_8: {
		"type": "weapon",
		"color": "purple",
		"chance": 750,
		"freq_mod": 0.6,
		"char": "HS",
		"extra_data": PlayerBulletTypes.HOMING_TRIPPLE_SUPER,
		"text": "Super Homing",
		"tts": SoundManager.tts_super_homing
	},
	PowerupType.WEAPON_9: {
		"type": "weapon",
		"color": "purple",
		"chance": 900,
		"freq_mod": 1.0,
		"char": "LS",
		"extra_data": PlayerBulletTypes.LASER_SINGLE,
		"text": "Laser",
		"tts": SoundManager.tts_laser
	},
	PowerupType.TIMED_SUPER_RAPIDFIRE: {
		"type": "time",
		"color": "green",
		"chance": 530,
		"freq_mod": 1.0,
		"char": "SR",
		"extra_data": null,
		"text": "Super Rapidfire",
		"tts": SoundManager.tts_super_rapidfire
	},
	PowerupType.CRYSTAL_MAGNET: {
		"type": "time",
		"color": "blue",
		"chance": 410,
		"freq_mod": 1.0,
		"char": "CM",
		"extra_data": null,
		"text": "Crystal Magnet",
		"tts": SoundManager.tts_crystal_magnet
	},
	PowerupType.TIMED_INVINCIBILITY: {
		"type": "time",
		"color": "red",
		"chance": 600,
		"freq_mod": 1.0,
		"char": "IV",
		"extra_data": null,
		"text": "Invincibility",
		"tts": SoundManager.tts_invincibility
	},
	PowerupType.TIMED_EXPLODE_TO_CRYSTAL: {
		"type": "time",
		"color": "blue",
		"chance": 403,
		"freq_mod": 1.0,
		"char": "CX",
		"extra_data": null,
		"text": "Crystal Explosion",
		"tts": SoundManager.tts_crystal_explosion
	},
	PowerupType.LUCK: {
		"type": "time",
		"color": "yellow", # in this case this is the only one and it's red colored
		"chance": 700,
		"freq_mod": 1.0,
		"char": "LK",
		"extra_data": null,
		"text": "Lucky You!",
		"tts": SoundManager.tts_increased_luck
	},
	PowerupType.CRISTAL_DOUBLER: {
		"type": "instant",
		"color": "yellow", # in this case this is the only one and it's red colored
		"chance": 810,
		"freq_mod": 1.0,
		"char": "2X",
		"extra_data": null,
		"text": "Crystal Doubler!",
		"tts": SoundManager.tts_crystal_doubler
	},
	PowerupType.MINE_FIELD: {
		"type": "instant",
		"color": "purple",
		"freq_mod": 1.0,
		"chance": 600,
		"char": "MF",
		"extra_data": null,
		"text": "Mine Field",
		"tts": SoundManager.tts_mine_field
	},
	PowerupType.CURSE: {
		"type": "curse",
		"color": "gray", # in this case this is the only one and it's red colored
		"freq_mod": 1.0,
		"chance": 300,
		"char": "X",
		"extra_data": null,
		"text": "Random Curse!",
		"tts": SoundManager.tts_damn_it
	},
	PowerupType.A: {
		"type": "curse",
		"color": "gray",
		"freq_mod": 1.0,
		"chance": 500,
		"char": "a",
		"extra_data": null,
		"text": "Cursed Module [a]",
		"tts": SoundManager.tts_cursed_module
	},
	PowerupType.B: {
		"type": "curse",
		"color": "gray",
		"chance": 500,
		"freq_mod": 1.0,
		"char": "b",
		"extra_data": null,
		"text": "Cursed Module [b]",
		"tts": SoundManager.tts_cursed_module
	},
	PowerupType.C: {
		"type": "curse",
		"color": "gray",
		"chance": 500,
		"freq_mod": 1.0,
		"char": "c",
		"extra_data": null,
		"text": "Cursed Module [c]",
		"tts": SoundManager.tts_cursed_module
	},

}

func setCheatState(wasCheat):
	lastCheatState = wasCheat

func getCheatState():
	return lastCheatState

func getBulletData(bulletType):
	var index = bulletType+1
	return powerups[PowerupType["WEAPON_"+str(index)]]


var crystals = {
	CrystalType.c5: {
		"value": 5,
		"animation": "c5",
		"chance": 100
	},
	CrystalType.c10: {
		"value": 10,
		"animation": "c10",
		"chance": 100
	},
	CrystalType.c20: {
		"value": 20,
		"animation": "c20",
		"chance": 150
	},
	CrystalType.c50: {
		"value": 50,
		"animation": "c50",
		"chance": 150
	},
	CrystalType.c100: {
		"value": 100,
		"animation": "c100",
		"chance": 200
	},
	CrystalType.c200: {
		"value": 200,
		"animation": "c200",
		"chance": 400
	}
}

enum LevelType {
	NORMAL,
	BOSS,
	BONUS,
	RUSH,
	DEBRIS
}

enum PlayerMode {
	INIT,
	NORMAL,
	FROZEN,
	DEAD
}

func calculatePowerupChance(powerupType) -> int:
	var _chance = powerups[powerupType]["chance"]
	_chance = (_chance / GameScene.getPlayerLuck()) + (GameScene.getProperDifficulty()*10)
	return _chance

func calculateCrystalChance(crystalType) -> int:
	var _chance = 0
	_chance = crystals[crystalType]["chance"]
	_chance = (_chance / GameScene.getPlayerLuck()) + (GameScene.getProperDifficulty()*10)
	return _chance

var _dropOnce = false

var sumChance = 0

func doDropPowerup():

	var selected = -1

	var chanceDiff = (Config.PowerupChanceBase-Config.PowerupChanceMin)
	var realChance = Config.PowerupChanceBase - int(min( chanceDiff , (chanceDiff/Config.PlayerMaxLuck)*GameScene.getPlayerLuck()))

	if isChance(randi(), realChance):
		# which powerup?

		if(sumChance==0):
			for powerup in powerups:
				var _chance = powerups[powerup]["chance"]
				sumChance += (maxPowerupChance-_chance)

		var rnd = randi() % sumChance

		var current = 0

		for powerup in powerups:
			var _chance = powerups[powerup]["chance"]
			current += (maxPowerupChance-_chance)
			if current>=rnd and selected==-1:
				selected = powerup

	# exception do not drop LUCK powerup if we have extra luck
	if selected == PowerupType.LUCK and Global.GameScene.hasPlayerEffect(Global.PlayerEffect.LUCK):
		selected = PowerupType.CURSE
			
	return selected



func _old_doDropPowerup():

	# debug
	if(!_dropOnce):
		_dropOnce = true
		# return PowerupType.MINE_FIELD
		# return PowerupType.CURSE
		# return PowerupType.CRISTAL_DOUBLER

	var powerupToDrop = -1

	for powerup in powerups:
		var _chance = calculatePowerupChance(powerup)
		if isChance(randi(), _chance):
			powerupToDrop = powerup
	
	return powerupToDrop

func doDropCrystal():

	# debug
	# return randi()%len(CrystalType);

	var crystalToDrop = -1

	for crystalType in crystals:
		var _chance = calculateCrystalChance(crystalType)
		if isChance(randi(), _chance):
			crystalToDrop = crystalType

	return crystalToDrop

func isEnemyActive(mode) -> bool:
	return mode == EnemyMode.ENTRY or mode == EnemyMode.IDLE or mode == EnemyMode.ATTACK

enum EntryStartPoints {
	TOP_LEFT,
	TOP_CENTER
	TOP_RIGHT,
	LEFT,
	RIGHT,
	BOTTOM_LEFT,
	BOTTOM_CENTER,
	BOTTOM_RIGHT,
}

enum MidPointLocation {
	TOP_LEFT,
	TOP_CENTER
	TOP_RIGHT,
	SCREEN_CENTER
	BOTTOM_LEFT,
	BOTTOM_CENTER,
	BOTTOM_RIGHT,
}

enum EntryExitPoints {
	TOP_LEFT,
	TOP_CENTER
	TOP_RIGHT,
	LEFT,
	RIGHT,
	BOTTOM_LEFT,
	BOTTOM_CENTER,
	BOTTOM_RIGHT,
}

enum WiggleMode {
	NORMAL,
	ROW,
	COL,
	COL_ROW,
	COUNT
}

enum GameDifficulty {
	EASY = 0,
	NORMAL = 1,
	HARD = 3,
	EXTREME = 5
}

func getDifficultyString(index):

	var _data = {
		0: "easy",
		1: "normal",
		3: "hard",
		5: "extreme"
	}

	return _data[index]

# Game scene reference, set from game so it can be accessed from anywhere
var GameScene = null

# Global variables
var BulletsOnScreen = 0

# Enemy bullets on screen
var EnemyBulletsOnScreen = 0

# Enemies attacking
var EnemiesAttacking = 0

# Object Pool System for Performance Optimization
var ObjectPools = {}

var SelectedDifficulty = GameDifficulty.NORMAL

func isOffScreen(pos:Vector2, padding = 100):
	var windowSize = getWindowSize()
	return pos.x < -padding or pos.x > windowSize.x + padding or pos.y < -padding or pos.y > windowSize.y + padding

func gameJustStartedFromMenu():
	pass

func isOffScreenBottom(pos:Vector2, padding = 100):
	var windowSize = getWindowSize()
	return pos.y > windowSize.y + padding

func isOffScreenTop(pos:Vector2, padding = 100):
	return pos.y < -padding

# Get window size
func getWindowSize():
	return Vector2(ProjectSettings.get_setting("display/window/size/width"),ProjectSettings.get_setting("display/window/size/height")) 

# Set timeout helper function
func setTimeout(parentNode, timeoutSec, object, callback, binds = []):
	var timer = Timer.new()
	timer.one_shot = true
	timer.wait_time = timeoutSec
	timer.connect("timeout", object, callback, binds)
	parentNode.add_child(timer)
	timer.start()

# Call a method if it exists
func callIfExists(object, method, callDeferred = false):
	if object.has_method(method):
		if callDeferred:
			object.call_deferred(method)
		else:
			object.call(method)
	else:
		Global.dbg("Method " + method + " does not exist on object " + object.name)

func getPlayer():
	return GameScene.player;

func getPlayerPosition():

	if(GameScene.isPlayerReady()):
		return GameScene.player.position
	else:
		return Config.ShipStartPos

var lastTts = 0

func playTts(name):
	if((Tick.ms()-lastTts) >500):
		# var _pitch = 0.9+randf()*0.2
		Global.playSound(name, Global.getWindowSize()/Vector2(2,2),1.0, 1.0)
		self.lastTts = Tick.ms()

func playSound(stream, position, volume_db = 0, pitch = 1.0):

	if(!OptionsData.isSoundOn):
		return

	var sound = AudioStreamPlayer2D.new()
	add_child(sound)
	sound.connect("finished", sound, "queue_free")
	sound.volume_db = volume_db
	sound.pitch_scale = pitch
	sound.stream = stream
	sound.position = position
	sound.play()

	return sound

func createTween(parentNode) -> Tween:
	var tween = Tween.new()
	parentNode.add_child(tween)
	return tween

func get_all_children(node) -> Array:
	var nodes : Array = []
	for N in node.get_children():
		if N.get_child_count() > 0:
			nodes.append(N)
			nodes.append_array(get_all_children(N))
		else:
			nodes.append(N)
	return nodes


# chaclulate chance
# rndi - random number
# probability - 1 in probability chance 
func isChance(rndi, probability) -> bool:
	return (rndi%(int(probability)) == (probability-1))

var throttleDic = {}

# Optimized throttling system with reduced dictionary lookups
var throttleCache = {}
const THROTTLE_CLEANUP_INTERVAL = 10000  # Clean up old entries every 10 seconds
var lastThrottleCleanup = 0

# centralized function to throttle things - optimized version
func doThrottle(identifier, timeout_msec, checkOnly = false, doReset = false):
	var currentTime = Tick.ms()

	# Periodic cleanup to prevent memory leaks
	if currentTime - lastThrottleCleanup > THROTTLE_CLEANUP_INTERVAL:
		cleanupOldThrottleEntries(currentTime)
		lastThrottleCleanup = currentTime

	if doReset:
		throttleDic[identifier] = currentTime
		return false

	# Use cached lookup for better performance
	var lastTime = throttleDic.get(identifier, -1)

	if lastTime == -1:
		if not checkOnly:
			throttleDic[identifier] = currentTime
		return false

	if currentTime - lastTime > timeout_msec:
		throttleDic[identifier] = currentTime
		return false

	return true

func cleanupOldThrottleEntries(currentTime: int):
	"""Remove old throttle entries to prevent memory bloat"""
	var keysToRemove = []
	for key in throttleDic.keys():
		if currentTime - throttleDic[key] > 60000:  # Remove entries older than 1 minute
			keysToRemove.append(key)

	for key in keysToRemove:
		throttleDic.erase(key)

# Object Pool Management Functions
func getPooledObject(poolName: String, scene: PackedScene):
	if not ObjectPools.has(poolName):
		ObjectPools[poolName] = []

	var pool = ObjectPools[poolName]

	if pool.size() > 0:
		var obj = pool.pop_back()
		obj.visible = true
		obj.set_process(true)
		obj.set_physics_process(true)
		return obj
	else:
		return scene.instance()

func returnToPool(poolName: String, obj: Node):
	if not ObjectPools.has(poolName):
		ObjectPools[poolName] = []

	# Reset object state
	obj.visible = false
	obj.set_process(false)
	obj.set_physics_process(false)

	# Remove from scene tree if it's in one
	if obj.get_parent():
		obj.get_parent().remove_child(obj)

	# Add to pool
	ObjectPools[poolName].push_back(obj)

func clearPool(poolName: String):
	if ObjectPools.has(poolName):
		for obj in ObjectPools[poolName]:
			if is_instance_valid(obj):
				obj.queue_free()
		ObjectPools[poolName].clear()

func clearAllPools():
	for poolName in ObjectPools.keys():
		clearPool(poolName)

func backToTitle():
	var _s = get_tree().change_scene("res://scenes/TitleScreen.tscn")

func CurrentLevelScene():
	return GameScene.get_node("CurrentLevel")

func getPercentage(value, total) -> float:
	return (float(value) / float(total)) * 100.0

func ifelse(condition, trueValue, falseValue):
	if condition:
		return trueValue
	else:
		return falseValue

enum DebrisType {
	Small,
	Large,
	Random
}

enum OISize {
   Small = -1,
   Medium = 0,
   Large = 1 
}

enum OIType {
	Green = 0,
	Yellow = 1,
	Red = 2,
	Animated = 3
}

var lastCheatState = false
var lastStatsObject = {}
var lastScoreObject = {}

func dbg(string, data=null):
	if(Config.Env.IsDevelopment):
		print(string, data)
