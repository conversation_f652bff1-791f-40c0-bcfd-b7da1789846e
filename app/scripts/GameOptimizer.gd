extends Node

# Game optimization manager - coordinates all performance optimizations
var gameScene = null
var isOptimizationEnabled = true

# Optimization settings
var dynamicQualityEnabled = true
var targetFPS = 60.0
var minFPS = 30.0

# Quality levels
enum QualityLevel {
	HIGH,
	MEDIUM,
	LOW,
	MINIMAL
}

var currentQuality = QualityLevel.HIGH

func _ready():
	gameScene = Global.GameScene
	set_process(true)

func _process(delta):
	if not isOptimizationEnabled or not gameScene:
		return
	
	if dynamicQualityEnabled:
		adjustQualityBasedOnPerformance()
	
	optimizeObjectCounts()
	cleanupInvalidObjects()

func adjustQualityBasedOnPerformance():
	"""Dynamically adjust quality based on current FPS"""
	if not gameScene.performanceMonitor:
		return
	
	var currentFPS = gameScene.performanceMonitor.currentFPS
	
	if currentFPS < minFPS:
		# Performance is poor, reduce quality
		if currentQuality < QualityLevel.MINIMAL:
			currentQuality += 1
			applyQualitySettings()
			print("Performance optimization: Reduced quality to level ", currentQuality)
	elif currentFPS > targetFPS * 0.9:
		# Performance is good, can increase quality
		if currentQuality > QualityLevel.HIGH:
			currentQuality -= 1
			applyQualitySettings()
			print("Performance optimization: Increased quality to level ", currentQuality)

func applyQualitySettings():
	"""Apply quality settings based on current quality level"""
	match currentQuality:
		QualityLevel.HIGH:
			setHighQuality()
		QualityLevel.MEDIUM:
			setMediumQuality()
		QualityLevel.LOW:
			setLowQuality()
		QualityLevel.MINIMAL:
			setMinimalQuality()

func setHighQuality():
	"""High quality settings"""
	Config.MaxPlayerBulletsOnScreen = 50
	Config.MaxEnemyBulletsOnScreen = 100
	Config.MaxSparkleEffects = 30
	Config.BulletUpdateBatchSize = 15

func setMediumQuality():
	"""Medium quality settings"""
	Config.MaxPlayerBulletsOnScreen = 35
	Config.MaxEnemyBulletsOnScreen = 70
	Config.MaxSparkleEffects = 20
	Config.BulletUpdateBatchSize = 12

func setLowQuality():
	"""Low quality settings"""
	Config.MaxPlayerBulletsOnScreen = 25
	Config.MaxEnemyBulletsOnScreen = 50
	Config.MaxSparkleEffects = 15
	Config.BulletUpdateBatchSize = 10

func setMinimalQuality():
	"""Minimal quality settings for maximum performance"""
	Config.MaxPlayerBulletsOnScreen = 15
	Config.MaxEnemyBulletsOnScreen = 30
	Config.MaxSparkleEffects = 10
	Config.BulletUpdateBatchSize = 8

func optimizeObjectCounts():
	"""Enforce object count limits"""
	if not gameScene:
		return
	
	# Limit player bullets
	if Global.BulletsOnScreen > Config.MaxPlayerBulletsOnScreen:
		cullOldestBullets("player", Global.BulletsOnScreen - Config.MaxPlayerBulletsOnScreen)
	
	# Limit enemy bullets
	if Global.EnemyBulletsOnScreen > Config.MaxEnemyBulletsOnScreen:
		cullOldestBullets("enemy", Global.EnemyBulletsOnScreen - Config.MaxEnemyBulletsOnScreen)

func cullOldestBullets(bulletType: String, cullCount: int):
	"""Remove oldest bullets to maintain performance"""
	if not gameScene.bulletManager:
		return
	
	var bulletsToRemove = []
	var activeBullets = gameScene.bulletManager.activeBullets
	
	# Find bullets of the specified type
	for bullet in activeBullets:
		if not is_instance_valid(bullet):
			continue
		
		var isTargetType = false
		if bulletType == "player" and bullet.has_method("getDamagePoint"):
			isTargetType = true
		elif bulletType == "enemy" and bullet.has_method("isEnemyBullet"):
			isTargetType = true
		
		if isTargetType:
			bulletsToRemove.append(bullet)
		
		if bulletsToRemove.size() >= cullCount:
			break
	
	# Remove the oldest bullets
	for bullet in bulletsToRemove:
		if bullet.has_method("destroy"):
			bullet.destroy(true)

func cleanupInvalidObjects():
	"""Clean up invalid objects from various systems"""
	if gameScene.bulletManager:
		gameScene.bulletManager.cleanupInvalidBullets()
	
	if gameScene.spatialGrid:
		# Clean up invalid objects from spatial grid
		var invalidObjects = []
		for obj in gameScene.spatialGrid.trackedObjects.keys():
			if not is_instance_valid(obj):
				invalidObjects.append(obj)
		
		for obj in invalidObjects:
			gameScene.spatialGrid.removeObject(obj)

func forceGarbageCollection():
	"""Force garbage collection to free memory"""
	# This is a hint to the engine to run garbage collection
	# Note: Godot doesn't have explicit GC control, but we can trigger it indirectly
	var temp_array = []
	for i in range(1000):
		temp_array.append(Vector2(i, i))
	temp_array.clear()

func getOptimizationStatus() -> Dictionary:
	"""Get current optimization status"""
	return {
		"enabled": isOptimizationEnabled,
		"quality_level": currentQuality,
		"dynamic_quality": dynamicQualityEnabled,
		"target_fps": targetFPS,
		"current_limits": {
			"player_bullets": Config.MaxPlayerBulletsOnScreen,
			"enemy_bullets": Config.MaxEnemyBulletsOnScreen,
			"sparkle_effects": Config.MaxSparkleEffects,
			"batch_size": Config.BulletUpdateBatchSize
		}
	}

func enableOptimization():
	"""Enable all optimizations"""
	isOptimizationEnabled = true
	print("Game optimizations enabled")

func disableOptimization():
	"""Disable optimizations (for debugging)"""
	isOptimizationEnabled = false
	currentQuality = QualityLevel.HIGH
	applyQualitySettings()
	print("Game optimizations disabled")

func setTargetFPS(fps: float):
	"""Set target FPS for dynamic quality adjustment"""
	targetFPS = fps
	minFPS = fps * 0.5  # Minimum FPS is 50% of target

func emergencyOptimization():
	"""Emergency optimization when performance is critically low"""
	print("Emergency optimization activated!")
	
	# Set minimal quality
	currentQuality = QualityLevel.MINIMAL
	applyQualitySettings()
	
	# Aggressively cull objects
	cullOldestBullets("player", Global.BulletsOnScreen / 2)
	cullOldestBullets("enemy", Global.EnemyBulletsOnScreen / 2)
	
	# Force cleanup
	cleanupInvalidObjects()
	forceGarbageCollection()
	
	# Clear object pools to free memory
	Global.clearAllPools()

func resetOptimization():
	"""Reset optimization to default settings"""
	currentQuality = QualityLevel.HIGH
	applyQualitySettings()
	isOptimizationEnabled = true
	dynamicQualityEnabled = true
	targetFPS = 60.0
	minFPS = 30.0
	print("Optimization settings reset to defaults")
